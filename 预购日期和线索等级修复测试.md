# 预购日期和线索等级字段修复测试

## 🔧 问题分析

### 预购日期字段
- **问题**: 这是一个日期选择器（`el-date-editor`），不是普通的下拉选择框
- **解决方案**: 专门的日期填充逻辑，支持多种设置方法和Vue组件事件

### 线索等级字段  
- **问题**: 选项可能是动态加载的，静态HTML中看不到选项列表
- **解决方案**: 特殊处理逻辑，多次尝试激活并等待选项加载

## 🎯 修复内容

### 1. 预购日期处理优化
```javascript
// 多种方法尝试设置日期值
- 直接设置input.value
- 触发focus/input/change/blur事件
- 触发Vue组件事件（如果是Vue组件）
- 多次验证和重试机制
```

### 2. 线索等级特殊处理
```javascript
// 专门的处理函数
- 多次点击尝试激活（最多3次）
- 每次等待1.5秒让选项加载
- 智能选项匹配（优先选择包含"A"和"7天"的选项）
- 详细的选项记录用于调试
```

## 📋 测试步骤

### 1. 重新加载插件
- 确保最新代码生效
- 检查配置选项已正确设置

### 2. 运行测试
- 进入客户跟进页面
- 启动自动跟进功能
- 重点观察这两个字段的处理日志

## 🔍 预期日志输出

### 预购日期成功的日志：
```
📅 开始填充日期字段: 预购日期
📅 准备设置日期: 2025-08-XX XX:XX
✅ 预购日期 日期设置成功: 2025-08-XX XX:XX
✅ 预购日期 日期填充成功
```

### 线索等级成功的日志：
```
🎯 特殊处理线索等级字段
🖱️ 线索等级第1次点击尝试
线索等级找到 X 个选项
线索等级可用选项: H（2天内跟进）, A（7天内跟进）, B（30天内跟进）
🎯 线索等级选择: A（7天内跟进）
✅ 线索等级选择成功: A（7天内跟进）
✅ 线索等级 处理成功
```

### 如果失败的日志：
```
❌ 预购日期 未找到日期输入框
⚠️ 预购日期 日期设置后验证失败，当前值: [实际值]
❌ 线索等级3次尝试均失败
```

## ⚠️ 故障排除

### 预购日期问题：
1. **输入框未找到**
   - 检查页面是否完全加载
   - 确认字段标签是否正确

2. **日期设置失败**
   - 查看是否有JavaScript错误
   - 检查日期格式是否正确
   - 确认没有其他脚本干扰

3. **验证失败**
   - 可能是Vue组件需要特殊事件
   - 尝试手动设置日期测试

### 线索等级问题：
1. **选项未加载**
   - 网络较慢时增加等待时间
   - 检查是否需要先选择其他字段

2. **找不到合适选项**
   - 查看日志中的"可用选项"列表
   - 确认选项文本是否与预期匹配

3. **多次尝试失败**
   - 手动测试点击选择框是否正常
   - 检查是否有权限或状态限制

## 🔧 调试技巧

### 1. 查看详细日志
- 关注每个步骤的执行结果
- 特别注意错误信息和可用选项列表

### 2. 手动测试
- 先手动操作这两个字段
- 确认正常的操作流程

### 3. 网络和性能
- 确保网络连接稳定
- 适当增加等待时间

## 📊 成功标准

### 预购日期：
- ✅ 能够找到日期输入框
- ✅ 成功设置未来7-30天的随机日期
- ✅ 日期格式正确（YYYY-MM-DD HH:mm）
- ✅ 设置后验证值正确

### 线索等级：
- ✅ 能够激活选择框
- ✅ 成功加载选项列表
- ✅ 找到并选择合适的等级选项
- ✅ 优先选择"A（7天内跟进）"

## 🚀 后续优化

如果基本功能正常，可以考虑：

### 1. 日期字段优化
- 支持更多日期格式
- 添加工作日验证
- 支持时间段限制

### 2. 等级字段优化
- 根据客户类型智能选择等级
- 支持更多等级选项
- 添加业务规则验证

### 3. 通用优化
- 增加重试次数配置
- 优化等待时间算法
- 添加更详细的错误处理

---

**重要提醒**：
1. 这两个字段的处理比较复杂，可能需要多次调试
2. 如果仍有问题，请提供完整的日志输出
3. 建议先在单个记录上测试，确认无误后再批量处理
